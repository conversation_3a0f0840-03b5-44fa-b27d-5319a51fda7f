import requests

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://wx.qmpsee.com",
    "Platform": "web",
    "Pragma": "no-cache",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "Source": "see",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "appflag": "see-h5-1.0.0",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
}

data = {
    "page": "1",
    "num": "20",
    "ca_uuid": "feef62bfdac45a94b9cd89aed5c235be",
    "appflag": "see-h5-1.0.0",
}

response = requests.post(
    "https://wyiosapi.qmpsee.com/Web/getCaDetail", headers=headers, data=data
)

print(response.json())
